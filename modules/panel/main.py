from fabric.system_tray.widgets import SystemTray
from fabric.system_tray.service import System<PERSON>ray<PERSON>tem as SystemTrayItemService
from fabric.utils import get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.datetime import DateTime
from fabric.widgets.label import Label
from fabric.widgets.revealer import Revealer
from gi.repository import Gtk, GdkPixbuf
from fabric.widgets.svg import Svg
from modules.panel.components.indicators import Indicators
from modules.panel.components.menubar import MenuBar
from utils.wayland import WaylandWindow as Window
import os

# Patch the SystemTrayItemService to handle missing icons gracefully
original_get_preferred_icon_pixbuf = SystemTrayItemService.get_preferred_icon_pixbuf


def patched_get_preferred_icon_pixbuf(self, size=None, resize_method="bilinear"):
    """Patched version that handles missing icons and file paths gracefully"""
    try:
        # Try the original method first
        return original_get_preferred_icon_pixbuf(self, size, resize_method)
    except Exception:
        # If the original method fails, try alternative approaches
        icon_name = self.icon_name
        attention_icon_name = self.attention_icon_name

        if self.status == "NeedsAttention" and attention_icon_name:
            preferred_icon_name = attention_icon_name
        else:
            preferred_icon_name = icon_name

        if not preferred_icon_name:
            return None

        # Check if it's a file path
        if preferred_icon_name.startswith('/') and os.path.exists(preferred_icon_name):
            try:
                # Load directly from file path
                pixbuf = GdkPixbuf.Pixbuf.new_from_file_at_size(
                    preferred_icon_name,
                    size or 24,
                    size or 24
                )
                return pixbuf
            except Exception:
                pass

        # Try with default GTK theme
        try:
            default_theme = Gtk.IconTheme.get_default()
            if default_theme.has_icon(preferred_icon_name):
                return default_theme.load_icon(
                    preferred_icon_name,
                    size or 24,
                    Gtk.IconLookupFlags.FORCE_SIZE
                )
        except Exception:
            pass

        # Try to extract just the filename without extension for theme lookup
        if '/' in preferred_icon_name:
            icon_basename = os.path.splitext(os.path.basename(preferred_icon_name))[0]
            try:
                default_theme = Gtk.IconTheme.get_default()
                if default_theme.has_icon(icon_basename):
                    return default_theme.load_icon(
                        icon_basename,
                        size or 24,
                        Gtk.IconLookupFlags.FORCE_SIZE
                    )
            except Exception:
                pass

        # Return None if all attempts fail - the widget will handle this
        return None


SystemTrayItemService.get_preferred_icon_pixbuf = patched_get_preferred_icon_pixbuf


class Panel(Window):
    def __init__(self, **kwargs):
        super().__init__(
            name="bar",
            layer="top",
            anchor="left top right",
            exclusivity="auto",
            visible=False,
        )

        self.launcher = kwargs.get("launcher", None)
        self.menubar = MenuBar()

        self.imac = Button(
            name="panel-button",
            child=Svg(
                size=24,
                svg_file=get_relative_path("../../config/assets/icons/logo.svg"),
            ),
            on_clicked=lambda *_: self.menubar.show_system_dropdown(self.imac),
        )
        self.notch_spot = Box(
            name="notch-spot",
            size=(200, 24),
            h_expand=True,
            v_expand=True,
            children=Label(label="notch"),
        )

        self.tray = SystemTray(name="system-tray", spacing=4, icon_size=20)

        self.tray_revealer = Revealer(
            name="tray-revealer",
            child=self.tray,
            child_revealed=False,
            transition_type="slide-left",
            transition_duration=300,
        )

        self.chevron_button = Button(
            name="panel-button",
            child=Svg(
                size=18,
                svg_file=get_relative_path(
                    "../../config/assets/icons/chevron-right.svg"
                ),
            ),
            on_clicked=self.toggle_tray,
        )

        self.indicators = Indicators()

        self.search = Button(
            name="panel-button",
            on_clicked=lambda *_: self.search_apps(),
            child=Svg(
                size=20,
                svg_file=get_relative_path("../../config/assets/icons/search.svg"),
            ),
        )

        self.controlcenter = Button(
            name="panel-button",
            child=Svg(
                size=24,
                svg_file=get_relative_path(
                    "../../config/assets/icons/control-center.svg"
                ),
            ),
        )

        self.children = CenterBox(
            name="panel",
            start_children=Box(
                name="modules-left",
                children=[
                    self.imac,
                    self.menubar,
                ],
            ),
            # center_children=Box(
            #     name="modules-center",
            #     children=self.notch_spot,
            # ),
            end_children=Box(
                name="modules-right",
                spacing=4,
                orientation="h",
                children=[
                    self.tray_revealer,
                    self.chevron_button,
                    self.indicators,
                    self.search,
                    self.controlcenter,
                    DateTime(name="date-time", formatters=["%a %b %d %I:%M"]),
                ],
            ),
        )

        return self.show_all()

    def search_apps(self):
        self.launcher.show_launcher()

    def toggle_tray(self, *_):
        current_state = self.tray_revealer.child_revealed
        self.tray_revealer.child_revealed = not current_state

        if self.tray_revealer.child_revealed:
            self.chevron_button.get_child().set_from_file(
                get_relative_path("../../config/assets/icons/chevron-left.svg")
            )
        else:
            self.chevron_button.get_child().set_from_file(
                get_relative_path("../../config/assets/icons/chevron-right.svg")
            )
